import { useState, useRef, useEffect } from 'react';
import { useAuth } from '../scripts/contexts/AuthContext.jsx';

const Header = ({ currentRoute, isDrawerOpen, setIsDrawerOpen, isMobile }) => {
  const { user, isAuthenticated, logout } = useAuth();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const drawerButtonRef = useRef(null);
  const drawerRef = useRef(null);
  const profileRef = useRef(null);

  // Close profile dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileRef.current && !profileRef.current.contains(event.target)) {
        setIsProfileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const handleLogout = async () => {
    setIsProfileOpen(false);
    await logout();
  };

  const navigationItems = [
    { href: '#/', label: 'Beranda', route: '/' },
    { href: '#/analisis', label: 'Analisis', route: '/analisis' },
    { href: '#/riwayat', label: 'Riwayat', route: '/riwayat' },
    { href: '#/artikel', label: 'Artikel', route: '/artikel' },
    { href: '#/about', label: 'About', route: '/about' },
  ];

  return (
    <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 flex justify-between items-center h-16">
        {/* Logo */}
        <a
          href="#/"
          className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-orange-500 bg-clip-text text-transparent no-underline"
        >
          Dermalyze
        </a>

        {/* Desktop Navigation */}
        <nav
          ref={drawerRef}
          className={`${
            isMobile ? (isDrawerOpen ? "block" : "hidden") : "block"
          } ${
            isMobile ? "absolute" : "static"
          } top-full left-0 right-0 bg-white ${
            isMobile ? "shadow-lg" : ""
          } z-40`}
        >
          <ul
            className={`flex ${isMobile ? "flex-col" : "flex-row"} ${
              isMobile ? "gap-0" : "gap-8"
            } list-none m-0 ${isMobile ? "p-4" : "p-0"}`}
          >
            {navigationItems.map((item) => (
              <li key={item.route}>
                <a
                  href={item.href}
                  className={`block px-3 py-2 text-sm font-medium no-underline transition-colors duration-200 ${
                    currentRoute === item.route
                      ? "text-orange-600 border-b-2 border-orange-600"
                      : "text-gray-700 hover:text-orange-600"
                  }`}
                >
                  {item.label}
                </a>
              </li>
            ))}
          </ul>
        </nav>

        {/* Right Side - Auth Section */}
        <div className="flex items-center space-x-4">
          {/* Authentication Section */}
          {isAuthenticated ? (
            <div className="relative" ref={profileRef}>
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 border border-gray-200"
              >
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <span className="hidden sm:block text-sm font-medium text-gray-700">
                  {user?.name || "User"}
                </span>
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Profile Dropdown */}
              {isProfileOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                  <a
                    href="#/profile"
                    className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 no-underline"
                    onClick={() => setIsProfileOpen(false)}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span>Profil</span>
                  </a>
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 text-left"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span>Keluar</span>
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <a
                href="#/login"
                className="text-sm font-medium text-gray-600 hover:text-orange-600 transition-colors duration-200 no-underline"
              >
                Masuk
              </a>
              <a
                href="#/register"
                className="bg-gradient-to-r from-orange-600 to-orange-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-orange-700 hover:to-orange-600 transition-all duration-200 no-underline"
              >
                Daftar
              </a>
            </div>
          )}

          {/* Mobile Menu Button */}
          <button
            ref={drawerButtonRef}
            onClick={toggleDrawer}
            className={`${
              isMobile ? "block" : "hidden"
            } bg-transparent border-none text-xl text-gray-700 cursor-pointer p-2 hover:text-orange-600`}
          >
            ☰
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
