const Hero = () => {
  const points = 0; // Temporary static value

  return (
    <div>
      {/* Hero Section */}
      <section
        className="bg-gradient-to-br from-orange-50 to-pink-50 py-20"
        aria-labelledby="hero-title"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              {/* Points Display Box */}
              <aside className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-orange-100 inline-block w-fit">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-lg font-bold">★</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 m-0">
                      Poin Anda
                    </p>
                    <p className="text-2xl font-bold text-orange-600 m-0">
                      {points}
                    </p>
                  </div>
                </div>
              </aside>

              {/* Main Title */}
              <header>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight mb-6">
                  Temukan Skincare yang Tepat untuk{" "}
                  <span className="text-orange-600">Kulitmu</span>
                </h1>
                <p className="text-lg text-gray-600 leading-relaxed mb-8">
                  Analisa wajah dengan teknologi machine learning untuk
                  rekomendasi perawatan kulit.
                </p>
              </header>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="#/analisis"
                  className="btn-primary inline-flex items-center justify-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  Mulai Analisis Kulit
                </a>

                <button className="btn-secondary inline-flex items-center justify-center">
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Pelajari Lebih Lanjut
                </button>
              </div>

              {/* Features List */}
              <ul className="text-sm text-gray-600 space-y-2 list-none p-0 m-0">
                <li>✨ Analisis AI yang akurat dalam hitungan detik</li>
                <li>📱 Upload foto atau ambil foto langsung</li>
                <li>💡 Rekomendasi perawatan yang personal</li>
              </ul>
            </div>

            {/* Right Content - Image */}
            <div className="relative">
              <figure className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                  alt="Wanita sedang melakukan rutinitas perawatan kulit dengan produk skincare"
                  className="w-full h-[500px] object-cover"
                />

                {/* Floating elements - decorative only */}
                <div className="absolute top-8 right-8 bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  </div>
                </div>

                <div className="absolute bottom-8 left-8 bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                  </div>
                </div>

                <div className="absolute top-1/2 left-8 bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                  <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                    <div className="w-4 h-4 bg-pink-500 rounded-full"></div>
                  </div>
                </div>
              </figure>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Hero;
