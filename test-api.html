<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test News API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>News API Test</h1>
    <button onclick="testAPI()">Test News API</button>
    <div id="results"></div>

    <script>
        const NEWS_API_KEY = '8b64d643aeef4d07a8757feafbe1227d';
        const NEWS_API_BASE_URL = 'https://newsapi.org/v2';
        const PROXY_URLS = [
            'https://api.allorigins.win/raw?url=',
            'https://cors-anywhere.herokuapp.com/',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        function addResult(message, type = 'result') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        async function testDirectAPI() {
            addResult('Testing direct API call...', 'loading');
            try {
                const url = `${NEWS_API_BASE_URL}/everything?q=skincare&language=en&pageSize=3&apiKey=${NEWS_API_KEY}`;
                const response = await fetch(url);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Direct API Success: Found ${data.articles.length} articles`, 'success');
                    return data.articles;
                } else {
                    addResult(`❌ Direct API Failed: ${response.status} - ${response.statusText}`, 'error');
                    return null;
                }
            } catch (error) {
                addResult(`❌ Direct API Error: ${error.message}`, 'error');
                return null;
            }
        }

        async function testProxyAPI() {
            for (let i = 0; i < PROXY_URLS.length; i++) {
                const proxyUrl = PROXY_URLS[i];
                addResult(`Testing proxy ${i + 1}: ${proxyUrl}`, 'loading');
                
                try {
                    const apiUrl = `${NEWS_API_BASE_URL}/everything?q=skincare&language=en&pageSize=3&apiKey=${NEWS_API_KEY}`;
                    const url = proxyUrl + encodeURIComponent(apiUrl);
                    
                    const response = await fetch(url);
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.articles && data.articles.length > 0) {
                            addResult(`✅ Proxy ${i + 1} Success: Found ${data.articles.length} articles`, 'success');
                            return data.articles;
                        } else {
                            addResult(`⚠️ Proxy ${i + 1}: No articles found`, 'error');
                        }
                    } else {
                        addResult(`❌ Proxy ${i + 1} Failed: ${response.status}`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ Proxy ${i + 1} Error: ${error.message}`, 'error');
                }
            }
            return null;
        }

        async function testAPI() {
            document.getElementById('results').innerHTML = '';
            addResult('Starting API tests...', 'loading');
            
            // Test direct API first
            let articles = await testDirectAPI();
            
            // If direct fails, try proxies
            if (!articles) {
                addResult('Direct API failed, trying proxies...', 'loading');
                articles = await testProxyAPI();
            }
            
            if (articles) {
                addResult('📰 Sample articles found:', 'success');
                articles.slice(0, 3).forEach((article, index) => {
                    addResult(`
                        <strong>${index + 1}. ${article.title}</strong><br>
                        <em>Source: ${article.source.name}</em><br>
                        <a href="${article.url}" target="_blank">Read Article</a><br>
                        <small>Published: ${article.publishedAt}</small>
                    `, 'success');
                });
            } else {
                addResult('❌ All API methods failed. Using fallback data in the app.', 'error');
            }
        }
    </script>
</body>
</html>
