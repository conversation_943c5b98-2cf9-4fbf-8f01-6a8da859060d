# 🧪 Testing Authentication System

## 📋 Overview
Sistem authentication sudah dileng<PERSON><PERSON> dengan mock functions untuk testing UI tanpa memerlukan backend yang aktif.

## 🔧 Mock Authentication Features

### ✅ **Yang Sudah Berfungsi:**
1. **Mock Login**: Simulasi login dengan validasi form
2. **Mock Register**: Simulasi registrasi dengan validasi
3. **Protected Routes**: Redirect ke login jika belum authenticated
4. **User State Management**: Menyimpan user data di localStorage
5. **Header Integration**: Menampilkan user dropdown setelah login

### 🎯 **Cara Testing:**

#### 1. **Test Protected Routes (Tanpa Login)**
- Buka: `http://localhost:5173/#/analisis`
- **Expected**: Menampilkan halaman "Login Diperlukan" dengan tombol login
- Klik "Login Sekarang" → redirect ke halaman login

#### 2. **Test Login Process**
- Buka: `http://localhost:5173/#/login`
- Masukkan email: `<EMAIL>`
- Masukkan password: `123456` (minimal 6 karakter)
- <PERSON><PERSON> "Masuk"
- **Expected**: 
  - Loading spinner selama 1 detik
  - Pesan "Login berhasil!"
  - Redirect ke home page
  - Header menampilkan dropdown user dengan nama "test"

#### 3. **Test Register Process**
- Buka: `http://localhost:5173/#/register`
- Masukkan nama: `Test User`
- Masukkan email: `<EMAIL>`
- Masukkan password: `123456`
- Masukkan confirm password: `123456`
- Klik "Daftar"
- **Expected**:
  - Loading spinner selama 1 detik
  - Pesan "Registrasi berhasil!"
  - Auto-login dan redirect ke home page
  - Header menampilkan dropdown user dengan nama "Test User"

#### 4. **Test Protected Routes (Setelah Login)**
- Setelah login, buka: `http://localhost:5173/#/analisis`
- **Expected**: Menampilkan halaman analisis normal (tidak redirect)

#### 5. **Test User Dropdown**
- Setelah login, klik dropdown user di header
- **Expected**: Menampilkan menu:
  - "Profil" → link ke `/profile`
  - "Keluar" → logout dan redirect ke home

#### 6. **Test Logout**
- Klik "Keluar" dari dropdown
- **Expected**:
  - User data dihapus dari localStorage
  - Header kembali menampilkan tombol "Masuk" dan "Daftar"
  - Redirect ke home page

#### 7. **Test Profile Page**
- Login terlebih dahulu
- Buka: `http://localhost:5173/#/profile`
- **Expected**: Menampilkan halaman profil dengan data user

## 🔍 **Validation Testing:**

### Login Form Validation:
- **Empty email**: "Email harus diisi"
- **Invalid email format**: "Format email tidak valid"
- **Empty password**: "Password harus diisi"
- **Short password**: "Password minimal 6 karakter"

### Register Form Validation:
- **Empty name**: "Nama harus diisi"
- **Short name**: "Nama minimal 2 karakter"
- **Empty email**: "Email harus diisi"
- **Invalid email**: "Format email tidak valid"
- **Empty password**: "Password harus diisi"
- **Short password**: "Password minimal 6 karakter"
- **Password mismatch**: "Password dan konfirmasi password tidak cocok"

## 🛠️ **Mock Data Structure:**

### Mock User Object:
```javascript
{
  id: "1",
  name: "test", // dari email prefix atau input nama
  email: "<EMAIL>",
  joinDate: "2024-01-01T00:00:00.000Z",
  points: 0,
  analysisCount: 0
}
```

### Mock Token:
```javascript
"mock_jwt_token_1704067200000" // timestamp-based
```

## 📱 **Browser Storage:**

Data disimpan di localStorage:
- `dermalyze_token`: Mock JWT token
- `dermalyze_user`: User object (JSON string)

## 🔄 **State Flow:**

1. **App Load**: Check localStorage untuk existing auth data
2. **Login Success**: Store token + user → Update UI state
3. **Protected Route Access**: Check auth state → Allow/Redirect
4. **Logout**: Clear localStorage → Reset UI state

## 🚀 **Ready for Backend Integration:**

Ketika backend sudah siap:
1. Uncomment real API calls di `authService.js`
2. Comment out mock functions
3. Update `baseUrl` di authService
4. Test dengan real backend endpoints

## 📝 **Console Logs:**

Untuk debugging, check browser console:
- `Mock login attempt: {email, password}`
- `Mock registration attempt: {name, email, password}`
- `ProtectedRoute - isAuthenticated: true/false`
- `Token verification skipped - backend not available`

## ✅ **Test Checklist:**

- [ ] Protected route redirect ke login
- [ ] Login form validation
- [ ] Login success flow
- [ ] Register form validation  
- [ ] Register success flow
- [ ] Header user dropdown
- [ ] Profile page access
- [ ] Logout functionality
- [ ] State persistence (refresh page)
- [ ] Protected route access after login

Semua fitur authentication UI sudah siap dan dapat ditest secara lengkap! 🎉
