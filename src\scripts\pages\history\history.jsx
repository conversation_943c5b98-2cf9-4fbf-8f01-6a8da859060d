import { useEffect, useState } from "react";
import ProtectedRoute from "../../../components/ProtectedRoute.jsx";
// import apiService from '../services/api'; // Commented out for now, using mock data

const history = () => {
  const [history, sethhistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAnalysis, setSelectedAnalysis] = useState(null);

  // Mock user ID - dalam implementasi nyata, ambil dari auth context
  const userId = "user123";

  useEffect(() => {
    fetchhistory();
  }, []);

  const fetchhistory = async () => {
    try {
      setLoading(true);
      // Untuk demo, kita gunakan data mock
      // const data = await apiService.gethistory(userId);

      // Mock data untuk demo
      const mockData = [
        {
          id: 1,
          date: "2024-01-15",
          time: "14:30",
          skinType: "Kombinasi",
          moisture: 65,
          oilLevel: "Sedang",
          concerns: ["Jerawat", "Pori-pori besar"],
          image: "https://via.placeholder.com/150x150",
          recommendations: 3,
        },
        {
          id: 2,
          date: "2024-01-10",
          time: "09:15",
          skinType: "Berminyak",
          moisture: 45,
          oilLevel: "Tinggi",
          concerns: ["Komedo", "Kilap berlebih"],
          image: "https://via.placeholder.com/150x150",
          recommendations: 5,
        },
        {
          id: 3,
          date: "2024-01-05",
          time: "16:45",
          skinType: "Kering",
          moisture: 30,
          oilLevel: "Rendah",
          concerns: ["Kulit kering", "Garis halus"],
          image: "https://via.placeholder.com/150x150",
          recommendations: 4,
        },
      ];

      sethistory(mockData);
    } catch (err) {
      setError("Gagal memuat riwayat analisis");
      console.error("Error fetching analysis history:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAnalysis = async (analysisId) => {
    if (window.confirm("Apakah Anda yakin ingin menghapus analisis ini?")) {
      try {
        // await apiService.deleteAnalysis(analysisId);
        sethistory((prev) => prev.filter((item) => item.id !== analysisId));
      } catch (err) {
        console.error("Error deleting analysis:", err);
        alert("Gagal menghapus analisis");
      }
    }
  };

  const handleViewDetails = (analysis) => {
    setSelectedAnalysis(analysis);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat riwayat analisis...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button onClick={fetchhistory} className="btn-primary">
            Coba Lagi
          </button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Riwayat Analisis
            </h1>
            <p className="text-gray-600">
              Lihat semua hasil analisis kulit Anda
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Total Analisis
              </h3>
              <p className="text-3xl font-bold text-orange-600">
                {history.length}
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Analisis Bulan Ini
              </h3>
              <p className="text-3xl font-bold text-blue-600">2</p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Rekomendasi Diterima
              </h3>
              <p className="text-3xl font-bold text-green-600">12</p>
            </div>
          </div>

          {/* Analysis History List */}
          {history.length === 0 ? (
            <div className="bg-white rounded-lg p-12 text-center shadow-sm">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Belum Ada Analisis
              </h3>
              <p className="text-gray-600 mb-6">
                Mulai analisis kulit pertama Anda untuk melihat riwayat di sini
              </p>
              <a href="/analisis" className="btn-primary">
                Mulai Analisis
              </a>
            </div>
          ) : (
            <div className="space-y-6">
              {history.map((analysis) => (
                <div
                  key={analysis.id}
                  className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <img
                        src={analysis.image}
                        alt="Hasil analisis"
                        className="w-20 h-20 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            Analisis {analysis.date}
                          </h3>
                          <span className="text-sm text-gray-500">
                            {analysis.time}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <p className="text-sm text-gray-600">Jenis Kulit</p>
                            <p className="font-semibold text-purple-600">
                              {analysis.skinType}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Kelembapan</p>
                            <p className="font-semibold text-blue-600">
                              {analysis.moisture}%
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">
                              Kadar Minyak
                            </p>
                            <p className="font-semibold text-yellow-600">
                              {analysis.oilLevel}
                            </p>
                          </div>
                        </div>

                        <div className="mb-4">
                          <p className="text-sm text-gray-600 mb-2">
                            Masalah Kulit:
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {analysis.concerns.map((concern, index) => (
                              <span
                                key={index}
                                className="px-3 py-1 bg-red-50 text-red-700 text-sm rounded-full"
                              >
                                {concern}
                              </span>
                            ))}
                          </div>
                        </div>

                        <p className="text-sm text-gray-600">
                          {analysis.recommendations} rekomendasi produk tersedia
                        </p>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleViewDetails(analysis)}
                        className="px-4 py-2 text-orange-600 border border-orange-600 rounded-lg hover:bg-orange-50 transition-colors"
                      >
                        Lihat Detail
                      </button>
                      <button
                        onClick={() => handleDeleteAnalysis(analysis.id)}
                        className="px-4 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50 transition-colors"
                      >
                        Hapus
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Detail Modal */}
        {selectedAnalysis && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Detail Analisis
                  </h2>
                  <button
                    onClick={() => setSelectedAnalysis(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  <img
                    src={selectedAnalysis.image}
                    alt="Hasil analisis"
                    className="w-full h-64 object-cover rounded-lg"
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Tanggal & Waktu</p>
                      <p className="font-semibold">
                        {selectedAnalysis.date} - {selectedAnalysis.time}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Jenis Kulit</p>
                      <p className="font-semibold text-purple-600">
                        {selectedAnalysis.skinType}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Kelembapan</p>
                      <p className="font-semibold text-blue-600">
                        {selectedAnalysis.moisture}%
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Kadar Minyak</p>
                      <p className="font-semibold text-yellow-600">
                        {selectedAnalysis.oilLevel}
                      </p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-2">Masalah Kulit:</p>
                    <div className="flex flex-wrap gap-2">
                      {selectedAnalysis.concerns.map((concern, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-red-50 text-red-700 text-sm rounded-full"
                        >
                          {concern}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-4">
                    <button className="flex-1 btn-primary">
                      Lihat Rekomendasi
                    </button>
                    <button className="flex-1 btn-secondary">
                      Analisis Ulang
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
};

export default history;
