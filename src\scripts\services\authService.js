// Authentication Service for handling login, signup, and user management
class AuthService {
  constructor() {
    this.baseUrl = "http://localhost:3000/api"; // Backend API URL - adjust as needed
    this.tokenKey = "dermalyze_token";
    this.userKey = "dermalyze_user";
  }

  // Get stored token
  getToken() {
    return localStorage.getItem(this.tokenKey);
  }

  // Get stored user data
  getUser() {
    const userData = localStorage.getItem(this.userKey);
    return userData ? JSON.parse(userData) : null;
  }

  // Store token and user data
  setAuthData(token, user) {
    localStorage.setItem(this.tokenKey, token);
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  // Clear auth data
  clearAuthData() {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = this.getToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Login user
  async login(email, password) {
    try {
      // Mock login for testing - replace with real API call when backend is ready
      console.log("Mock login attempt:", { email, password });

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock validation
      if (!email || !password) {
        return {
          success: false,
          message: "Email dan password harus diisi",
        };
      }

      if (password.length < 6) {
        return {
          success: false,
          message: "Password minimal 6 karakter",
        };
      }

      // Mock successful login
      const mockUser = {
        id: "1",
        name: email.split("@")[0], // Use email prefix as name
        email: email,
        joinDate: new Date().toISOString(),
        points: 0,
        analysisCount: 0,
      };

      const mockToken = "mock_jwt_token_" + Date.now();

      // Store auth data
      this.setAuthData(mockToken, mockUser);

      return {
        success: true,
        user: mockUser,
        token: mockToken,
        message: "Login berhasil",
      };

      // Uncomment below when backend is ready:
      /*
      const response = await fetch(`${this.baseUrl}/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Login failed");
      }

      // Store auth data
      this.setAuthData(data.token, data.user);

      return {
        success: true,
        user: data.user,
        token: data.token,
        message: "Login berhasil",
      };
      */
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        message: error.message || "Terjadi kesalahan saat login",
      };
    }
  }

  // Register new user
  async register(userData) {
    try {
      const { name, email, password, confirmPassword } = userData;

      // Mock registration for testing - replace with real API call when backend is ready
      console.log("Mock registration attempt:", { name, email, password });

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Validate passwords match
      if (password !== confirmPassword) {
        return {
          success: false,
          message: "Password dan konfirmasi password tidak cocok",
        };
      }

      // Mock validation
      if (!name || !email || !password) {
        return {
          success: false,
          message: "Semua field harus diisi",
        };
      }

      if (password.length < 6) {
        return {
          success: false,
          message: "Password minimal 6 karakter",
        };
      }

      // Mock successful registration
      const mockUser = {
        id: "1",
        name: name,
        email: email,
        joinDate: new Date().toISOString(),
        points: 0,
        analysisCount: 0,
      };

      const mockToken = "mock_jwt_token_" + Date.now();

      // Automatically login after successful registration
      this.setAuthData(mockToken, mockUser);

      return {
        success: true,
        user: mockUser,
        token: mockToken,
        message: "Registrasi berhasil",
      };

      // Uncomment below when backend is ready:
      /*
      const response = await fetch(`${this.baseUrl}/auth/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Registration failed");
      }

      // Automatically login after successful registration
      this.setAuthData(data.token, data.user);

      return {
        success: true,
        user: data.user,
        token: data.token,
        message: "Registrasi berhasil",
      };
      */
    } catch (error) {
      console.error("Registration error:", error);
      return {
        success: false,
        message: error.message || "Terjadi kesalahan saat registrasi",
      };
    }
  }

  // Logout user
  async logout() {
    try {
      const token = this.getToken();

      if (token) {
        // Optional: Call backend logout endpoint
        await fetch(`${this.baseUrl}/auth/logout`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });
      }
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Always clear local auth data
      this.clearAuthData();
    }
  }

  // Get user profile
  async getProfile() {
    try {
      const token = this.getToken();

      if (!token) {
        throw new Error("No authentication token");
      }

      const response = await fetch(`${this.baseUrl}/auth/profile`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to get profile");
      }

      // Update stored user data
      this.setAuthData(token, data.user);

      return {
        success: true,
        user: data.user,
      };
    } catch (error) {
      console.error("Get profile error:", error);
      return {
        success: false,
        message: error.message || "Terjadi kesalahan saat mengambil profil",
      };
    }
  }

  // Update user profile
  async updateProfile(userData) {
    try {
      const token = this.getToken();

      if (!token) {
        throw new Error("No authentication token");
      }

      const response = await fetch(`${this.baseUrl}/auth/profile`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to update profile");
      }

      // Update stored user data
      this.setAuthData(token, data.user);

      return {
        success: true,
        user: data.user,
        message: "Profil berhasil diperbarui",
      };
    } catch (error) {
      console.error("Update profile error:", error);
      return {
        success: false,
        message: error.message || "Terjadi kesalahan saat memperbarui profil",
      };
    }
  }

  // Verify token validity
  async verifyToken() {
    try {
      const token = this.getToken();

      if (!token) {
        return false;
      }

      // For now, just return true if token exists
      // Real verification will be implemented when backend is ready
      console.log("Token verification skipped - backend not available");
      return true;

      // Uncomment below when backend is ready:
      /*
      const response = await fetch(`${this.baseUrl}/auth/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        this.clearAuthData();
        return false;
      }

      return true;
      */
    } catch (error) {
      console.error("Token verification error:", error);
      this.clearAuthData();
      return false;
    }
  }
}

export default new AuthService();
