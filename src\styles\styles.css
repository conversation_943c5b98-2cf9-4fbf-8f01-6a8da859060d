@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "Inter", system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, #ea580c, #f97316);
  color: white;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #dc2626, #ea580c);
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  padding: 12px 24px;
  border: 2px solid #ea580c;
  color: #ea580c;
  font-weight: 600;
  border-radius: 8px;
  background: white;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #ea580c;
  color: white;
  transform: translateY(-1px);
}

.btn-camera {
  padding: 16px 28px;
  border: 2px solid #ea580c;
  color: #ea580c;
  font-weight: 600;
  font-size: 16px;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px -2px rgba(234, 88, 12, 0.2);
}

.btn-camera:hover {
  background: #ea580c;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 12px 20px -4px rgba(234, 88, 12, 0.3);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Accessibility and Semantic Styles */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus styles for better accessibility */
*:focus {
  outline: 2px solid #ea580c;
  outline-offset: 2px;
}

/* Skip navigation link */
.skip-nav {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #ea580c;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-nav:focus {
  top: 6px;
}

/* Semantic HTML elements styling */
article {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
}

section {
  position: relative;
}

figure {
  margin: 0;
}

figcaption {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #1f2937;
  }

  .btn-primary {
    border: 2px solid #9a3412;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Footer button links styling */
.footer-link-button {
  color: #d1d5db;
  background: transparent;
  border: none;
  padding: 0;
  text-align: left;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  cursor: pointer;
  transition: color 0.2s ease;
}

.footer-link-button:hover {
  color: white;
}

.footer-link-button:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
