import { createContext, useContext, useEffect, useState } from "react";

const PointsContext = createContext();

export const usePoints = () => {
  const context = useContext(PointsContext);
  if (!context) {
    throw new Error("usePoints must be used within a PointsProvider");
  }
  return context;
};

export const PointsProvider = ({ children }) => {
  const [points, setPoints] = useState(0);
  const [readingSessions, setReadingSessions] = useState(new Set());

  // Load points from localStorage on mount
  useEffect(() => {
    const savedPoints = localStorage.getItem("dermalyze_points");
    const savedSessions = localStorage.getItem("dermalyze_reading_sessions");

    if (savedPoints) {
      setPoints(parseInt(savedPoints, 10));
    }

    if (savedSessions) {
      setReadingSessions(new Set(JSON.parse(savedSessions)));
    }
  }, []);

  // Save points to localStorage whenever points change
  useEffect(() => {
    localStorage.setItem("dermalyze_points", points.toString());
  }, [points]);

  // Save reading sessions to localStorage whenever sessions change
  useEffect(() => {
    localStorage.setItem(
      "dermalyze_reading_sessions",
      JSON.stringify([...readingSessions])
    );
  }, [readingSessions]);

  const addPoints = (amount, reason = "") => {
    setPoints((prevPoints) => prevPoints + amount);

    // Optional: Log the points addition for debugging
    console.log(
      `Points added: +${amount} (${reason}). Total: ${points + amount}`
    );
  };

  const subtractPoints = (amount, reason = "") => {
    setPoints((prevPoints) => Math.max(0, prevPoints - amount));

    // Optional: Log the points subtraction for debugging
    console.log(
      `Points subtracted: -${amount} (${reason}). Total: ${Math.max(
        0,
        points - amount
      )}`
    );
  };

  const awardReadingPoints = (articleId) => {
    // Check if user has already earned points for this article
    if (!readingSessions.has(articleId)) {
      addPoints(10, `Reading article ${articleId} for 3 minutes`);
      setReadingSessions((prev) => new Set([...prev, articleId]));
      return true; // Points were awarded
    }
    return false; // Points already awarded for this article
  };

  const resetPoints = () => {
    setPoints(0);
    setReadingSessions(new Set());
    localStorage.removeItem("dermalyze_points");
    localStorage.removeItem("dermalyze_reading_sessions");
  };

  const value = {
    points,
    addPoints,
    subtractPoints,
    awardReadingPoints,
    resetPoints,
    readingSessions: [...readingSessions],
  };

  return (
    <PointsContext.Provider value={value}>{children}</PointsContext.Provider>
  );
};
