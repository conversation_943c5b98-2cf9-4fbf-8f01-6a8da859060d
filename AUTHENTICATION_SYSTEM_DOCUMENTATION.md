# 🔐 Authentication System Documentation

## 📋 Overview
Sistem authentication lengkap untuk aplikasi Dermalyze dengan integrasi backend API, state management, dan protected routes.

## 🏗️ Architecture

### 1. **AuthService** (`src/scripts/services/authService.js`)
Service layer untuk komunikasi dengan backend API:
- **Login**: `POST /api/auth/login`
- **Register**: `POST /api/auth/register`
- **Logout**: `POST /api/auth/logout`
- **Get Profile**: `GET /api/auth/profile`
- **Update Profile**: `PUT /api/auth/profile`
- **Verify Token**: `GET /api/auth/verify`

### 2. **AuthContext** (`src/scripts/contexts/AuthContext.jsx`)
React Context untuk state management:
- User state management
- Authentication status
- Loading states
- Auth methods (login, register, logout, updateProfile)

### 3. **Protected Routes** (`src/components/ProtectedRoute.jsx`)
Komponen wrapper untuk halaman yang memerlukan authentication:
- Automatic redirect ke login jika belum authenticated
- Loading state saat verifikasi token
- User-friendly login prompt

## 🔧 Backend API Integration

### Base Configuration
```javascript
baseUrl: 'http://localhost:3000/api' // Adjust sesuai backend URL
```

### API Endpoints Expected

#### 1. **POST /api/auth/login**
```json
// Request
{
  "email": "<EMAIL>",
  "password": "password123"
}

// Response (Success)
{
  "success": true,
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "joinDate": "2024-01-01",
    "points": 0,
    "analysisCount": 0
  },
  "message": "Login successful"
}

// Response (Error)
{
  "success": false,
  "message": "Invalid credentials"
}
```

#### 2. **POST /api/auth/register**
```json
// Request
{
  "name": "User Name",
  "email": "<EMAIL>",
  "password": "password123"
}

// Response (Success)
{
  "success": true,
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "joinDate": "2024-01-01",
    "points": 0,
    "analysisCount": 0
  },
  "message": "Registration successful"
}
```

#### 3. **GET /api/auth/profile**
```json
// Headers
{
  "Authorization": "Bearer jwt_token_here"
}

// Response
{
  "success": true,
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "joinDate": "2024-01-01",
    "points": 150,
    "analysisCount": 5
  }
}
```

#### 4. **PUT /api/auth/profile**
```json
// Headers
{
  "Authorization": "Bearer jwt_token_here"
}

// Request
{
  "name": "Updated Name",
  "email": "<EMAIL>"
}

// Response
{
  "success": true,
  "user": {
    "id": "user_id",
    "name": "Updated Name",
    "email": "<EMAIL>",
    "joinDate": "2024-01-01",
    "points": 150,
    "analysisCount": 5
  },
  "message": "Profile updated successfully"
}
```

#### 5. **GET /api/auth/verify**
```json
// Headers
{
  "Authorization": "Bearer jwt_token_here"
}

// Response (Valid Token)
{
  "success": true,
  "valid": true
}

// Response (Invalid Token)
{
  "success": false,
  "valid": false
}
```

## 📱 Frontend Pages

### 1. **Login Page** (`/login`)
- Email dan password input
- Form validation
- Password visibility toggle
- Redirect ke home setelah login sukses
- Link ke register page

### 2. **Register Page** (`/register`)
- Nama, email, password, confirm password input
- Form validation
- Password matching validation
- Auto-login setelah register sukses
- Link ke login page

### 3. **Profile Page** (`/profile`)
- View dan edit user profile
- Protected route (requires authentication)
- Display user stats (total analisis, poin, join date)
- Update profile functionality

## 🛡️ Protected Routes

Halaman yang memerlukan authentication:
- `/analisis` - Halaman analisis kulit
- `/riwayat` - Halaman riwayat analisis
- `/profile` - Halaman profil user

## 🔄 State Management

### AuthContext provides:
```javascript
{
  user,              // User object atau null
  isAuthenticated,   // Boolean status
  isLoading,         // Loading state
  login,             // Function untuk login
  register,          // Function untuk register
  logout,            // Function untuk logout
  updateProfile,     // Function untuk update profile
  refreshProfile     // Function untuk refresh user data
}
```

## 💾 Local Storage

Data yang disimpan di localStorage:
- `dermalyze_token`: JWT token
- `dermalyze_user`: User object (JSON string)

## 🎨 UI Components

### Header dengan Authentication
- Menampilkan login/register buttons jika belum authenticated
- Menampilkan user dropdown dengan nama dan avatar jika authenticated
- Dropdown menu: Profil, Keluar

### User Dropdown Menu
- **Profil**: Link ke halaman profile
- **Keluar**: Logout dan redirect ke home

## 🔧 Usage Examples

### Using AuthContext in Components
```javascript
import { useAuth } from '../contexts/AuthContext.jsx';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();
  
  if (isAuthenticated) {
    return <div>Welcome, {user.name}!</div>;
  }
  
  return <div>Please login</div>;
}
```

### Protecting a Route
```javascript
import ProtectedRoute from '../components/ProtectedRoute.jsx';

function MyProtectedPage() {
  return (
    <ProtectedRoute>
      <div>This content requires authentication</div>
    </ProtectedRoute>
  );
}
```

## 🚀 Setup Instructions

1. **Install Dependencies**: Sudah included dalam React project
2. **Configure Backend URL**: Update `baseUrl` di `authService.js`
3. **Wrap App dengan AuthProvider**: Sudah implemented di `app.jsx`
4. **Add Routes**: Sudah added di `routes.jsx`
5. **Test Authentication Flow**: Login → Protected Page → Logout

## 🔍 Testing

### Manual Testing Steps:
1. **Register**: Buat akun baru di `/register`
2. **Login**: Login dengan akun yang dibuat di `/login`
3. **Protected Access**: Akses `/analisis` atau `/riwayat`
4. **Profile**: Update profile di `/profile`
5. **Logout**: Logout dari dropdown menu
6. **Protected Redirect**: Coba akses protected page setelah logout

## 🛠️ Backend Requirements

Backend harus menyediakan:
- JWT token authentication
- User registration dan login
- Profile management
- Token verification
- CORS configuration untuk frontend domain

## 📝 Notes

- Token disimpan di localStorage (consider httpOnly cookies untuk production)
- Auto-redirect ke login page untuk protected routes
- Graceful error handling untuk network issues
- Responsive design untuk mobile dan desktop
- Form validation dengan user feedback
